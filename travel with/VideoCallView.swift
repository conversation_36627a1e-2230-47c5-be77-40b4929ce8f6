//
//  VideoCallView.swift
//  travel with
//
//  Created by AI Assistant on 2025/8/1.
//

import SwiftUI
import AVFoundation
import Speech

// MARK: - 对话消息数据模型
struct ConversationMessage: Identifiable {
    let id = UUID()
    let content: String
    let isFromUser: Bool
    let timestamp = Date()
}

struct VideoCallView: View {
    @EnvironmentObject var aiService: AIService
    @Environment(\.dismiss) private var dismiss
    @StateObject private var cameraManager = CameraManager()
    @StateObject private var ttsService = TTSService.shared
    @State private var conversationMessages: [ConversationMessage] = []
    @State private var currentDisplayingMessage: ConversationMessage?
    @State private var isDisplayingMessage = false
    @State private var messageDisplayTimer: Timer?
    @State private var isRecording = false
    @State private var isMuted = false

    // 语音识别相关
    @State private var speechRecognizer: SFSpeechRecognizer?
    @State private var recognitionRequest: SFSpeechAudioBufferRecognitionRequest?
    @State private var recognitionTask: SFSpeechRecognitionTask?
    @State private var audioEngine = AVAudioEngine()
    @State private var lastSpeechTime = Date()
    @State private var speechTimer: Timer?
    @State private var currentSpeechText = ""
    @State private var isListening = false
    @State private var isProcessingSpeech = false
    
    var body: some View {
        GeometryReader { geometry in
            VStack(spacing: 0) {
                // 上部分：摄像头拍摄区域 (9:16比例)
                cameraPreviewSection(geometry: geometry)
                
                // 下部分：AI角色形象显示区域
                aiCharacterSection(geometry: geometry)
            }
        }
        .navigationBarHidden(true)
        .onAppear {
            setupVideoCall()
        }
        .onDisappear {
            cleanupVideoCall()
        }
    }
    
    // MARK: - 摄像头预览区域
    @ViewBuilder
    private func cameraPreviewSection(geometry: GeometryProxy) -> some View {
        let containerWidth = geometry.size.width * 0.95 // 容器宽度为屏幕宽度的95%
        let containerHeight = containerWidth * (3.0 / 4.0) // 横屏显示，宽高比4:3

        ZStack {
            // 背景
            LinearGradient(
                colors: [.black.opacity(0.8), .black.opacity(0.6)],
                startPoint: .top,
                endPoint: .bottom
            )

            VStack(spacing: 20) {
                // 顶部控制按钮
            HStack {
                // 返回按钮
                Button(action: {
                    print("退出按钮被点击")
                    dismiss()
                }) {
                    Image(systemName: "xmark.circle.fill")
                        .font(.title)
                        .foregroundColor(.white)
                        .frame(width: 44, height: 44)
                        .background(Color.black.opacity(0.6))
                        .clipShape(Circle())
                }
                .buttonStyle(PlainButtonStyle())
                .contentShape(Rectangle()) // 扩大点击区域
                .frame(width: 60, height: 60) // 增大整体点击区域

                Spacer()

                // 通话状态
                HStack(spacing: 4) {
                    Circle()
                        .fill(Color.green)
                        .frame(width: 8, height: 8)
                        .scaleEffect(1.5)
                        .animation(.easeInOut(duration: 1.0).repeatForever(), value: true)

                    Text("视频通话中")
                        .font(.caption)
                        .foregroundColor(.white)
                }
                .padding(.horizontal, 12)
                .padding(.vertical, 6)
                .background(Color.black.opacity(0.6))
                .cornerRadius(15)

                Spacer()

                // 摄像头切换按钮
                Button(action: {
                    print("翻转按钮被点击")
                    cameraManager.switchCamera()
                }) {
                    Image(systemName: "camera.rotate")
                        .font(.title)
                        .foregroundColor(.white)
                        .frame(width: 44, height: 44)
                        .background(Color.black.opacity(0.6))
                        .clipShape(Circle())
                }
                .buttonStyle(PlainButtonStyle())
                .contentShape(Rectangle()) // 扩大点击区域
                .frame(width: 60, height: 60) // 增大整体点击区域
            }
            .padding(.horizontal, 25)
            .padding(.top, 20) // 再向上移动一点点

            // 摄像头预览容器
            ZStack {
                // 容器背景和边框
                RoundedRectangle(cornerRadius: 20)
                    .fill(Color.black)
                    .stroke(Color.white.opacity(0.3), lineWidth: 2)
                    .frame(width: containerWidth, height: containerHeight)

                // 摄像头预览（横屏16:9显示，中心裁剪）
                CameraPreviewView(cameraManager: cameraManager)
                    .aspectRatio(contentMode: .fill) // 填充模式，会进行裁剪
                    .frame(width: containerWidth - 4, height: containerHeight - 4)
                    .clipShape(RoundedRectangle(cornerRadius: 18))
            }

                Spacer()
            }
        }
        .frame(height: geometry.size.height * 0.5) // 摄像头区域占屏幕高度的50%
    }
    
    // MARK: - AI角色形象显示区域
    @ViewBuilder
    private func aiCharacterSection(geometry: GeometryProxy) -> some View {
        let cameraHeight = geometry.size.height * 0.5
        let remainingHeight = geometry.size.height - cameraHeight
        let containerWidth = geometry.size.width * 0.95 // 与上半部分保持一致
        let containerHeight = remainingHeight - 15 // 容器高度几乎到底，只留15的底部间距

        VStack(spacing: 15) {
            // AI形象容器
            ZStack {
                // 容器背景和边框
                RoundedRectangle(cornerRadius: 20)
                    .fill(Color.black.opacity(0.3))
                    .stroke(Color.white.opacity(0.3), lineWidth: 2)
                    .frame(width: containerWidth, height: containerHeight)

                VStack(spacing: 0) {
                    // 对话栏区域（位于容器上部）
                    conversationArea
                        .frame(height: containerHeight * 0.28)
                        .padding(.top, 15)

                    // AI形象显示区域（位于容器中间）
                    aiAvatarArea
                        .frame(height: containerHeight * 0.72)
                }
                .frame(width: containerWidth - 4, height: containerHeight - 4)
                .clipShape(RoundedRectangle(cornerRadius: 18))
            }

            Spacer(minLength: 15) // 最小底部间距
        }
        .frame(height: remainingHeight)
        .background(
            LinearGradient(
                colors: [.black.opacity(0.6), .black.opacity(0.8)],
                startPoint: .top,
                endPoint: .bottom
            )
        )
    }
    
    // MARK: - 对话栏区域
    @ViewBuilder
    private var conversationArea: some View {
        // 对话消息显示
        ScrollViewReader { proxy in
            ScrollView {
                LazyVStack(spacing: 8) {
                    ForEach(conversationMessages) { message in
                        ConversationBubbleView(message: message)
                            .id(message.id)
                    }

                    // 当前正在显示的消息
                    if let currentMessage = currentDisplayingMessage {
                        ConversationBubbleView(message: currentMessage, isAnimating: isDisplayingMessage)
                            .id(currentMessage.id)
                    }
                }
                .padding(.horizontal, 15)
            }
            .onChange(of: conversationMessages.count) {
                // 延迟滚动，避免与动画冲突
                DispatchQueue.main.asyncAfter(deadline: .now() + 0.1) {
                    if let lastMessage = conversationMessages.last {
                        withAnimation(.easeOut(duration: 0.15)) {
                            proxy.scrollTo(lastMessage.id, anchor: .bottom)
                        }
                    }
                }
            }
            .onChange(of: currentDisplayingMessage?.id) {
                // 只在新消息出现且不在动画中时滚动
                if let currentMessage = currentDisplayingMessage, !isDisplayingMessage {
                    DispatchQueue.main.asyncAfter(deadline: .now() + 0.05) {
                        withAnimation(.easeOut(duration: 0.15)) {
                            proxy.scrollTo(currentMessage.id, anchor: .bottom)
                        }
                    }
                }
            }
        }
    }
    
    // MARK: - AI形象区域
    @ViewBuilder
    private var aiAvatarArea: some View {
        VStack {
            Spacer()

            // AI形象显示
            VStack(spacing: 20) {
                // AI头像
                Image(systemName: "person.circle.fill")
                    .font(.system(size: 100))
                    .foregroundStyle(
                        LinearGradient(
                            colors: [.orange, .pink, .purple],
                            startPoint: .topLeading,
                            endPoint: .bottomTrailing
                        )
                    )
                    .shadow(color: .orange.opacity(0.3), radius: 10, x: 0, y: 0)

                // AI名称
                Text("AI朋友")
                    .font(.title)
                    .fontWeight(.medium)
                    .foregroundColor(.white)

                // 状态指示
                HStack(spacing: 8) {
                    Circle()
                        .fill(ttsService.isPlaying ? Color.orange : Color.green)
                        .frame(width: 10, height: 10)
                        .scaleEffect(ttsService.isPlaying ? 1.5 : 1.2)
                        .animation(.easeInOut(duration: ttsService.isPlaying ? 0.6 : 1.5).repeatForever(), value: ttsService.isPlaying)

                    Text(ttsService.isPlaying ? "正在说话" : "在线")
                        .font(.subheadline)
                        .foregroundColor(ttsService.isPlaying ? .orange : .green)
                        .animation(.easeInOut(duration: 0.3), value: ttsService.isPlaying)
                }

                // TTS播放进度指示器（仅在播放时显示）
                if ttsService.isPlaying {
                    VStack(spacing: 8) {
                        // 语音波形动画
                        HStack(spacing: 4) {
                            ForEach(0..<5, id: \.self) { index in
                                RoundedRectangle(cornerRadius: 2)
                                    .fill(Color.orange.opacity(0.8))
                                    .frame(width: 3, height: CGFloat.random(in: 8...20))
                                    .animation(
                                        .easeInOut(duration: 0.5)
                                        .repeatForever()
                                        .delay(Double(index) * 0.1),
                                        value: ttsService.isPlaying
                                    )
                            }
                        }
                    }
                    .padding(.top, 8)
                }
            }

            Spacer()
        }
    }
    
    // MARK: - 设置视频通话
    private func setupVideoCall() {
        cameraManager.startSession()
        setupSpeechRecognition()
        startListening()

        // AI欢迎消息 - 立即显示
        displayMessage("我正在听你说话，请随时和我聊天吧！", isFromUser: false)
    }
    
    // MARK: - 清理视频通话
    private func cleanupVideoCall() {
        print("🧹 开始清理视频通话资源")

        // 停止摄像头
        cameraManager.stopSession()

        // 停止TTS播放
        ttsService.stopPlaying()

        // 清理消息显示定时器
        messageDisplayTimer?.invalidate()
        messageDisplayTimer = nil

        // 完全停止语音识别
        stopListening()

        // 清理语音定时器
        speechTimer?.invalidate()
        speechTimer = nil

        // 重置语音识别相关状态
        currentSpeechText = ""
        isProcessingSpeech = false

        // 停用音频会话
        do {
            try AVAudioSession.sharedInstance().setActive(false, options: .notifyOthersOnDeactivation)
            print("✅ 音频会话已停用")
        } catch {
            print("❌ 停用音频会话失败: \(error)")
        }

        print("✅ 视频通话资源清理完成")
    }



    // MARK: - 发送消息给AI
    private func sendMessageToAI(_ text: String) async {
        // 发送消息给AI服务
        await aiService.sendTextMessage(text)

        // 获取AI回复并显示
        if let lastMessage = aiService.messages.last, !lastMessage.isFromUser {
            await MainActor.run {
                displayMessage(lastMessage.content, isFromUser: false)
                // 触发TTS播放AI回复
                ttsService.synthesizeAndPlay(text: lastMessage.content)
            }

            // 等待TTS开始播放后再监听完成
            await waitForTTSToStart()
            await MainActor.run {
                startMonitoringTTSCompletion()
            }
        }
    }
    
    // MARK: - 显示消息（按句子逐一显示）
    private func displayMessage(_ text: String, isFromUser: Bool) {
        let sentences = splitIntoSentences(text)
        displaySentences(sentences, isFromUser: isFromUser, index: 0)
    }
    
    // MARK: - 分句显示
    private func displaySentences(_ sentences: [String], isFromUser: Bool, index: Int) {
        guard index < sentences.count else { return }

        let sentence = sentences[index].trimmingCharacters(in: .whitespacesAndNewlines)
        guard !sentence.isEmpty else {
            // 跳过空句子
            displaySentences(sentences, isFromUser: isFromUser, index: index + 1)
            return
        }

        let message = ConversationMessage(content: sentence, isFromUser: isFromUser)

        // 开始显示动画
        currentDisplayingMessage = message
        isDisplayingMessage = true

        // 更温和的显示效果，减少抖动
        DispatchQueue.main.asyncAfter(deadline: .now() + 0.1) {
            withAnimation(.easeInOut(duration: 0.2)) {
                isDisplayingMessage = false
            }
        }

        // 显示完成后添加到消息列表
        DispatchQueue.main.asyncAfter(deadline: .now() + 1.0) {
            // 使用批量更新减少UI重绘
            withAnimation(.none) {
                conversationMessages.append(message)
                currentDisplayingMessage = nil
            }

            // 继续显示下一句
            DispatchQueue.main.asyncAfter(deadline: .now() + 0.4) {
                displaySentences(sentences, isFromUser: isFromUser, index: index + 1)
            }
        }
    }
    
    // MARK: - 分句处理
    private func splitIntoSentences(_ text: String) -> [String] {
        // 按照中文和英文的句号、问号、感叹号分句
        let sentences = text.components(separatedBy: CharacterSet(charactersIn: "。！？.!?"))
            .map { $0.trimmingCharacters(in: .whitespacesAndNewlines) }
            .filter { !$0.isEmpty }

        return sentences.isEmpty ? [text] : sentences
    }

    // MARK: - 语音识别设置
    private func setupSpeechRecognition() {
        speechRecognizer = SFSpeechRecognizer(locale: Locale(identifier: "zh-CN"))

        // 请求语音识别权限
        SFSpeechRecognizer.requestAuthorization { authStatus in
            DispatchQueue.main.async {
                switch authStatus {
                case .authorized:
                    print("✅ 语音识别权限已授权")
                case .denied:
                    print("❌ 语音识别权限被拒绝")
                case .restricted:
                    print("❌ 语音识别权限受限")
                case .notDetermined:
                    print("⚠️ 语音识别权限未确定")
                @unknown default:
                    print("❓ 未知的语音识别权限状态")
                }
            }
        }

        // 请求麦克风权限
        AVAudioSession.sharedInstance().requestRecordPermission { granted in
            DispatchQueue.main.async {
                if granted {
                    print("✅ 麦克风权限已授权")
                } else {
                    print("❌ 麦克风权限被拒绝")
                }
            }
        }
    }

    // MARK: - 开始监听
    private func startListening() {
        print("🎯 尝试开始语音监听...")

        guard let speechRecognizer = speechRecognizer, speechRecognizer.isAvailable else {
            print("❌ 语音识别不可用")
            return
        }

        // 优化：检查是否可以快速恢复而不是完全重建
        if canQuickResume() {
            print("🚀 快速恢复语音监听")
            quickResumeListening()
        } else {
            print("🛑 停止之前的监听会话")
            stopListening()

            // 减少延迟：从0.3s降低到0.1s
            DispatchQueue.main.asyncAfter(deadline: .now() + 0.1) {
                self.setupAudioSessionForRecording()
            }
        }
    }

    // MARK: - 检查是否可以快速恢复
    private func canQuickResume() -> Bool {
        // 如果音频引擎已经准备好且没有运行，可以快速恢复
        return !audioEngine.isRunning &&
               recognitionRequest == nil &&
               recognitionTask == nil &&
               !isListening
    }

    // MARK: - 快速恢复监听
    private func quickResumeListening() {
        // 设置录音音频会话
        setupAudioSessionForRecording()
    }

    // MARK: - 设置录音音频会话
    private func setupAudioSessionForRecording() {
        print("🎙️ 开始设置录音音频会话")

        // 配置音频会话
        let audioSession = AVAudioSession.sharedInstance()
        do {
            // 先停用当前会话
            try audioSession.setActive(false, options: .notifyOthersOnDeactivation)

            // 设置录音会话
            try audioSession.setCategory(.record, mode: .measurement, options: .duckOthers)
            try audioSession.setActive(true, options: .notifyOthersOnDeactivation)

            print("✅ 录音音频会话配置成功")
        } catch {
            print("❌ 音频会话配置失败: \(error)")
            return
        }

        // 等待音频会话稳定
        DispatchQueue.main.asyncAfter(deadline: .now() + 0.2) {
            self.createRecognitionRequest()
        }
    }

    // MARK: - 创建识别请求
    private func createRecognitionRequest() {
        // 创建识别请求
        recognitionRequest = SFSpeechAudioBufferRecognitionRequest()
        guard let recognitionRequest = recognitionRequest else {
            print("❌ 无法创建语音识别请求")
            return
        }

        recognitionRequest.shouldReportPartialResults = true
        print("✅ 语音识别请求创建成功")

        // 安全地配置音频引擎
        setupAudioEngineWithTap(recognitionRequest: recognitionRequest)
    }

    // MARK: - 安全设置音频引擎Tap
    private func setupAudioEngineWithTap(recognitionRequest: SFSpeechAudioBufferRecognitionRequest) {
        print("🔧 开始设置音频引擎Tap")

        let inputNode = audioEngine.inputNode

        // 完全重置音频引擎
        if audioEngine.isRunning {
            audioEngine.stop()
            print("🛑 音频引擎已停止")
        }

        // 安全移除现有的tap
        do {
            inputNode.removeTap(onBus: 0)
            print("✅ 现有tap已移除")
        } catch {
            print("⚠️ 移除tap警告: \(error)")
        }

        // 等待音频引擎完全重置
        DispatchQueue.main.asyncAfter(deadline: .now() + 0.3) {
            self.validateAndInstallTap(inputNode: inputNode, recognitionRequest: recognitionRequest)
        }
    }

    // MARK: - 验证并安装Tap
    private func validateAndInstallTap(inputNode: AVAudioInputNode, recognitionRequest: SFSpeechAudioBufferRecognitionRequest) {
        let recordingFormat = inputNode.outputFormat(forBus: 0)

        print("🎵 音频格式检查: 采样率=\(recordingFormat.sampleRate)Hz, 通道数=\(recordingFormat.channelCount)")

        // 验证音频格式有效性
        guard recordingFormat.sampleRate > 0 && recordingFormat.channelCount > 0 else {
            print("❌ 音频格式无效，尝试重新初始化")

            // 重新初始化音频引擎
            audioEngine = AVAudioEngine()

            // 延迟重试
            DispatchQueue.main.asyncAfter(deadline: .now() + 0.5) {
                self.setupAudioEngineWithTap(recognitionRequest: recognitionRequest)
            }
            return
        }

        // 使用标准格式作为备选
        let standardFormat = AVAudioFormat(standardFormatWithSampleRate: 16000, channels: 1)
        let finalFormat = recordingFormat.sampleRate > 0 ? recordingFormat : standardFormat!

        print("✅ 使用音频格式: 采样率=\(finalFormat.sampleRate)Hz, 通道数=\(finalFormat.channelCount)")

        // 安装tap
        do {
            inputNode.installTap(onBus: 0, bufferSize: 1024, format: finalFormat) { buffer, _ in
                recognitionRequest.append(buffer)
            }
            print("✅ 音频tap安装成功")

            // 启动音频引擎和识别
            self.startAudioEngineAndRecognition(recognitionRequest: recognitionRequest)

        } catch {
            print("❌ 安装tap失败: \(error)")

            // 如果失败，尝试使用标准格式重试一次
            if finalFormat != standardFormat {
                print("🔄 尝试使用标准格式重试")
                DispatchQueue.main.asyncAfter(deadline: .now() + 0.2) {
                    self.retryWithStandardFormat(inputNode: inputNode, recognitionRequest: recognitionRequest)
                }
            }
        }
    }

    // MARK: - 使用标准格式重试
    private func retryWithStandardFormat(inputNode: AVAudioInputNode, recognitionRequest: SFSpeechAudioBufferRecognitionRequest) {
        guard let standardFormat = AVAudioFormat(standardFormatWithSampleRate: 16000, channels: 1) else {
            print("❌ 无法创建标准音频格式")
            return
        }

        print("🔄 使用标准格式重试: 16000Hz, 1通道")

        do {
            inputNode.installTap(onBus: 0, bufferSize: 1024, format: standardFormat) { buffer, _ in
                recognitionRequest.append(buffer)
            }
            print("✅ 标准格式tap安装成功")

            // 启动音频引擎和识别
            self.startAudioEngineAndRecognition(recognitionRequest: recognitionRequest)

        } catch {
            print("❌ 标准格式tap安装也失败: \(error)")
        }
    }

    // MARK: - 启动音频引擎和识别
    private func startAudioEngineAndRecognition(recognitionRequest: SFSpeechAudioBufferRecognitionRequest) {

        // 启动音频引擎
        audioEngine.prepare()
        do {
            try audioEngine.start()
            isListening = true
            print("✅ 开始语音监听")
        } catch {
            print("❌ 音频引擎启动失败: \(error)")
            return
        }

        // 开始识别任务
        guard let speechRecognizer = speechRecognizer else {
            print("❌ 语音识别器不可用")
            return
        }

        recognitionTask = speechRecognizer.recognitionTask(with: recognitionRequest) { result, error in
            Task { @MainActor in
                if let result = result {
                    let recognizedText = result.bestTranscription.formattedString

                    // 如果正在处理语音，忽略新的识别结果
                    guard !self.isProcessingSpeech else {
                        return
                    }

                    self.currentSpeechText = recognizedText
                    self.lastSpeechTime = Date()

                    // 重置定时器 - 改为2秒停顿
                    self.speechTimer?.invalidate()
                    self.speechTimer = Timer.scheduledTimer(withTimeInterval: 2.0, repeats: false) { _ in
                        Task { @MainActor in
                            self.processSpeechResult()
                        }
                    }

                    print("🎤 实时识别: \(recognizedText)")
                }

                if let error = error {
                    let nsError = error as NSError

                    // 处理不同类型的错误
                    switch nsError.code {
                    case 301: // Recognition request was canceled
                        print("ℹ️ 语音识别被取消（正常情况）")
                        // 这是正常的取消，不需要重新开始监听
                        return

                    case 1110: // No speech detected
                        print("ℹ️ 未检测到语音输入")
                        // 这是正常情况，继续监听

                    default:
                        print("❌ 语音识别错误: \(error)")
                    }

                    // 对于非取消错误，延迟重新开始监听
                    if nsError.code != 301 {
                        Task { @MainActor in
                            try? await Task.sleep(nanoseconds: 2_000_000_000) // 2秒
                            print("🔄 错误恢复：重新开始语音监听")
                            self.startListening()
                        }
                    }
                }
            }
        }
    }

    // MARK: - 停止监听
    private func stopListening() {
        print("🛑 开始停止语音监听")

        // 停止识别任务
        if let task = recognitionTask {
            task.cancel()
            recognitionTask = nil
            print("✅ 语音识别任务已取消")
        }

        // 安全地停止音频引擎和移除tap
        if audioEngine.isRunning {
            audioEngine.stop()
            print("✅ 音频引擎已停止")
        }

        // 安全地移除tap
        do {
            let inputNode = audioEngine.inputNode
            inputNode.removeTap(onBus: 0)
            print("✅ 音频tap已移除")
        } catch {
            print("⚠️ 移除音频tap时出现警告: \(error)")
        }

        // 结束识别请求
        if let request = recognitionRequest {
            request.endAudio()
            recognitionRequest = nil
            print("✅ 语音识别请求已结束")
        }

        // 重置所有状态
        isListening = false
        currentSpeechText = ""

        // 清理定时器
        speechTimer?.invalidate()
        speechTimer = nil

        print("✅ 语音监听完全停止")
    }

    // MARK: - 处理语音识别结果
    private func processSpeechResult() {
        // 防止重复处理
        guard !isProcessingSpeech else {
            print("ℹ️ 正在处理语音，忽略重复调用")
            return
        }

        // 设置处理标志
        isProcessingSpeech = true

        // 先停止当前的语音识别，避免冲突
        speechTimer?.invalidate()
        speechTimer = nil

        guard !currentSpeechText.trimmingCharacters(in: .whitespacesAndNewlines).isEmpty else {
            print("ℹ️ 没有识别到有效内容，继续监听")
            isProcessingSpeech = false
            // 如果没有识别到内容，继续当前的监听会话
            return
        }

        let finalText = currentSpeechText.trimmingCharacters(in: .whitespacesAndNewlines)
        print("📝 最终识别文本: \(finalText)")

        // 清空当前文本
        currentSpeechText = ""

        // 停止当前识别会话
        recognitionTask?.finish()

        // 显示用户消息
        displayMessage(finalText, isFromUser: true)

        // 发送给AI
        Task {
            await sendMessageToAI(finalText)
            // 注意：重新开始监听的逻辑现在在startMonitoringTTSCompletion()中处理
        }
    }

    // MARK: - 等待TTS开始播放
    private func waitForTTSToStart() async {
        print("🎵 等待TTS开始播放...")

        // 等待TTS开始播放，最多等待5秒
        for _ in 0..<50 {
            if ttsService.isPlaying {
                print("🎵 TTS已开始播放")
                return
            }
            try? await Task.sleep(nanoseconds: 100_000_000) // 100ms
        }

        print("⚠️ TTS启动超时，继续监听完成状态")
    }

    // MARK: - TTS完成监听
    private func startMonitoringTTSCompletion() {
        print("🎵 开始监听TTS播放完成")

        // 使用Task来异步监听TTS播放状态
        Task { @MainActor in
            var wasPlaying = ttsService.isPlaying

            while isProcessingSpeech {
                let currentlyPlaying = ttsService.isPlaying

                // 检测从播放状态变为非播放状态
                if wasPlaying && !currentlyPlaying {
                    print("🎵 检测到TTS播放完成，准备重新开始语音监听")

                    // 短暂延迟确保状态稳定
                    try? await Task.sleep(nanoseconds: 500_000_000) // 0.5秒

                    // 再次确认TTS确实停止了
                    if !ttsService.isPlaying && isProcessingSpeech {
                        isProcessingSpeech = false
                        print("🔄 重新开始语音监听")
                        startListening()
                        break
                    }
                }

                wasPlaying = currentlyPlaying

                // 每100毫秒检查一次
                try? await Task.sleep(nanoseconds: 100_000_000)
            }
        }
    }
}

// MARK: - 对话气泡视图
struct ConversationBubbleView: View {
    let message: ConversationMessage
    var isAnimating: Bool = false

    var body: some View {
        HStack {
            if message.isFromUser {
                Spacer(minLength: 50)
                messageContent
                    .background(
                        RoundedRectangle(cornerRadius: 16)
                            .fill(LinearGradient(
                                colors: [.blue, .blue.opacity(0.8)],
                                startPoint: .topLeading,
                                endPoint: .bottomTrailing
                            ))
                    )
            } else {
                HStack(alignment: .top, spacing: 8) {
                    // AI头像
                    Image(systemName: "heart.circle.fill")
                        .font(.title3)
                        .foregroundStyle(
                            LinearGradient(
                                colors: [.orange, .pink],
                                startPoint: .topLeading,
                                endPoint: .bottomTrailing
                            )
                        )

                    messageContent
                        .background(
                            RoundedRectangle(cornerRadius: 16)
                                .fill(Color.white.opacity(0.9))
                                .shadow(color: .black.opacity(0.1), radius: 2, x: 0, y: 1)
                        )
                }
                Spacer(minLength: 50)
            }
        }
        .scaleEffect(isAnimating ? 0.98 : 1.0)
        .opacity(isAnimating ? 0.8 : 1.0)
        .animation(.easeInOut(duration: 0.25), value: isAnimating)
    }

    @ViewBuilder
    private var messageContent: some View {
        VStack(alignment: .leading, spacing: 4) {
            Text(message.content)
                .font(.body)
                .foregroundColor(message.isFromUser ? .white : .primary)

            // 时间戳
            Text(message.timestamp, style: .time)
                .font(.caption2)
                .foregroundColor(message.isFromUser ? .white.opacity(0.7) : .secondary)
        }
        .padding(.horizontal, 12)
        .padding(.vertical, 8)
    }
}
