//
//  TTSService.swift
//  travel with
//
//  Created by AI Assistant on 2025/8/2.
//  基于火山引擎TTS WebSocket API实现
//  音色: zh_female_shuangkuaisisi_moon_bigtts (中文女声)
//  压缩: 无压缩
//

import Foundation
import AVFoundation
import Combine

// MARK: - TTS状态枚举
enum TTSState: Equatable {
    case idle
    case connecting
    case connected
    case synthesizing
    case playing
    case paused
    case error(String)

    static func == (lhs: TTSState, rhs: TTSState) -> Bool {
        switch (lhs, rhs) {
        case (.idle, .idle),
             (.connecting, .connecting),
             (.connected, .connected),
             (.synthesizing, .synthesizing),
             (.playing, .playing),
             (.paused, .paused):
            return true
        case (.error(let lhsMessage), .error(let rhsMessage)):
            return lhsMessage == rhsMessage
        default:
            return false
        }
    }
}

// MARK: - TTS请求结构 (基于demo格式)
struct TTSRequest: Codable {
    let app: AppInfo
    let user: UserInfo
    let audio: AudioInfo
    let request: RequestInfo

    struct AppInfo: Codable {
        let appid: String
        let token: String
        let cluster: String
    }

    struct UserInfo: Codable {
        let uid: String
    }

    struct AudioInfo: Codable {
        let voice_type: String
        let encoding: String
    }

    struct RequestInfo: Codable {
        let reqid: String
        let text: String
        let operation: String
        let extra_param: String
        let with_timestamp: String
    }
}

// MARK: - TTS服务管理类
@MainActor
class TTSService: NSObject, ObservableObject {
    static let shared = TTSService()

    // MARK: - 配置信息
    private let appId = "1771796339"
    private let accessToken = "MduxvCjM28XnWnXS_a2_NAedHCJ9649D"
    private let wsEndpoint = "wss://openspeech.bytedance.com/api/v1/tts/ws_binary"

    // MARK: - 发布属性
    @Published var currentState: TTSState = .idle
    @Published var isPlaying = false
    @Published var currentText = ""
    @Published var playbackProgress: Double = 0.0
    @Published var errorMessage: String?

    // MARK: - 私有属性
    private var webSocketTask: URLSessionWebSocketTask?
    private var urlSession: URLSession?
    private var audioPlayer: AVAudioPlayer?
    private var audioData = Data()
    private var currentRequestId: String?

    // 流式播放相关
    private var audioChunks: [Data] = []
    private var isStreamingStarted = false
    private var streamingTimer: Timer?

    // 连接复用相关
    private var isWebSocketConnected = false
    private var connectionRetryCount = 0
    private let maxRetryCount = 3
    private var heartbeatTimer: Timer?

    // MARK: - 音频设置 (根据用户要求配置)
    private let defaultVoice = "zh_female_shuangkuaisisi_moon_bigtts"  // 中文女声 (甜美音色)
    private let audioEncoding = "wav"

    override init() {
        super.init()
        setupAudioSession()
        setupURLSession()
    }

    // MARK: - 音频会话设置 (优化：使用对话友好的配置)
    private func setupAudioSession() {
        do {
            let audioSession = AVAudioSession.sharedInstance()
            // 使用.playAndRecord类别，支持同时播放和录音，更适合对话场景
            try audioSession.setCategory(.playAndRecord, mode: .voiceChat, options: [.defaultToSpeaker, .allowBluetooth])
            try audioSession.setActive(true)
            print("✅ 音频会话配置成功 (对话模式)")
        } catch {
            print("❌ 音频会话配置失败: \(error)")
            errorMessage = "音频会话设置失败: \(error.localizedDescription)"
        }
    }

    // MARK: - URL会话设置
    private func setupURLSession() {
        let config = URLSessionConfiguration.default
        config.timeoutIntervalForRequest = 30
        config.timeoutIntervalForResource = 60
        urlSession = URLSession(configuration: config, delegate: self, delegateQueue: nil)
    }

    // MARK: - 公共接口

    /// 合成并播放文本 (优化：复用WebSocket连接)
    func synthesizeAndPlay(text: String) {
        guard !text.trimmingCharacters(in: .whitespacesAndNewlines).isEmpty else {
            print("❌ 合成文本为空")
            return
        }

        // 只停止音频播放，保持WebSocket连接
        stopAudioOnly()

        currentText = text
        errorMessage = nil
        currentState = .connecting

        print("🎤 开始TTS合成: \(text)")
        print("🎵 使用音色: \(defaultVoice) (中文女声)")
        print("📦 压缩方式: 无压缩")

        Task {
            await ensureConnectionAndSynthesize(text: text)
        }
    }

    /// 只停止音频播放，保持连接
    private func stopAudioOnly() {
        audioPlayer?.stop()
        audioPlayer = nil

        // 清理流式播放状态
        streamingTimer?.invalidate()
        streamingTimer = nil
        audioChunks.removeAll()
        isStreamingStarted = false

        isPlaying = false
        playbackProgress = 0.0
        audioData = Data()
        currentRequestId = nil

        print("⏹️ 停止TTS播放 (保持连接)")
    }

    /// 停止播放 (AI整句回复结束时调用)
    func stopPlaying() {
        audioPlayer?.stop()
        audioPlayer = nil
        webSocketTask?.cancel(with: .goingAway, reason: nil)
        webSocketTask = nil

        // 清理流式播放状态
        streamingTimer?.invalidate()
        streamingTimer = nil
        audioChunks.removeAll()
        isStreamingStarted = false

        isPlaying = false
        playbackProgress = 0.0
        currentText = ""
        currentState = .idle
        audioData = Data()
        currentRequestId = nil

        print("⏹️ 停止TTS播放 (AI回复结束)")
    }

    /// 暂停播放
    func pausePlaying() {
        guard isPlaying else { return }
        audioPlayer?.pause()
        isPlaying = false
        currentState = .paused
        print("⏸️ 暂停TTS播放")
    }

    /// 继续播放
    func resumePlaying() {
        guard currentState == .paused, let player = audioPlayer else { return }
        player.play()
        isPlaying = true
        currentState = .playing
        print("▶️ 继续TTS播放")
    }

    // MARK: - 确保连接并合成 (优化版本)
    private func ensureConnectionAndSynthesize(text: String) async {
        // 检查现有连接是否可用
        if isWebSocketConnected && webSocketTask?.state == .running {
            print("🔄 复用现有WebSocket连接")
            await sendTTSRequest(text: text)
            return
        }

        // 需要建立新连接
        print("🔗 建立新的WebSocket连接")
        await connectAndSynthesize(text: text)
    }

    // MARK: - WebSocket连接和合成 (基于demo实现)
    private func connectAndSynthesize(text: String) async {
        guard let url = URL(string: wsEndpoint) else {
            await MainActor.run {
                errorMessage = "WebSocket URL无效"
                currentState = .error("WebSocket URL无效")
            }
            return
        }

        // 创建WebSocket连接 (按照API文档格式)
        var request = URLRequest(url: url)
        request.setValue("Bearer; \(accessToken)", forHTTPHeaderField: "Authorization")

        webSocketTask = urlSession?.webSocketTask(with: request)
        webSocketTask?.resume()

        await MainActor.run {
            currentState = .connected
        }

        // 标记连接状态
        isWebSocketConnected = true
        connectionRetryCount = 0

        print("🔗 WebSocket连接已建立")

        // 开始接收消息
        startReceivingMessages()

        // 启动心跳机制
        startHeartbeat()

        // 发送TTS请求
        await sendTTSRequest(text: text)
    }

    private func sendTTSRequest(text: String) async {
        let requestId = UUID().uuidString
        currentRequestId = requestId

        // 构建请求 (按照API文档格式)
        let ttsRequest = TTSRequest(
            app: TTSRequest.AppInfo(
                appid: appId,
                token: accessToken,  // Fake token，可传任意非空字符串
                cluster: "volcano_tts"  // 固定为volcano_tts
            ),
            user: TTSRequest.UserInfo(
                uid: UUID().uuidString
            ),
            audio: TTSRequest.AudioInfo(
                voice_type: defaultVoice,  // 中文女声
                encoding: audioEncoding
            ),
            request: TTSRequest.RequestInfo(
                reqid: requestId,
                text: text,
                operation: "submit",  // 流式操作
                extra_param: "{\"disable_markdown_filter\": false}",
                with_timestamp: "1"
            )
        )

        do {
            let jsonData = try JSONEncoder().encode(ttsRequest)
            let message = createFullClientRequestMessage(payload: jsonData)

            try await webSocketTask?.send(.data(message))

            await MainActor.run {
                currentState = .synthesizing
            }

            print("📤 发送TTS请求: \(requestId)")
            print("🎵 音色: \(defaultVoice) (中文女声)")
            print("📝 文本: \(text)")
        } catch {
            await MainActor.run {
                errorMessage = "发送TTS请求失败: \(error.localizedDescription)"
                currentState = .error("发送请求失败")
            }
            print("❌ 发送TTS请求失败: \(error)")
        }
    }


    // MARK: - 消息创建 (按照API文档格式，无压缩)
    private func createFullClientRequestMessage(payload: Data) -> Data {
        var message = Data()

        // 基础头部 (按照API文档格式)
        message.append(0x11) // version=1, headerSize=1 (4字节)
        message.append(0x10) // type=FullClientRequest(1), flag=NoSeq(0)
        message.append(0x10) // serialization=JSON(1), compression=None(0) - 无压缩
        message.append(0x00) // 填充字节

        // Payload大小 (4字节，大端序)
        let payloadSize = UInt32(payload.count).bigEndian
        message.append(contentsOf: withUnsafeBytes(of: payloadSize) { Array($0) })

        // Payload数据
        message.append(payload)

        print("📦 创建消息: 大小=\(message.count)字节, Payload=\(payload.count)字节, 压缩=无")

        return message
    }

    // MARK: - 消息处理 (基于demo逻辑)
    private func startReceivingMessages() {
        guard let webSocketTask = webSocketTask else { return }

        webSocketTask.receive { [weak self] result in
            switch result {
            case .success(let message):
                Task {
                    await self?.handleWebSocketMessage(message)
                    self?.startReceivingMessages() // 继续接收下一条消息
                }
            case .failure(let error):
                Task {
                    await self?.handleWebSocketError(error)
                }
            }
        }
    }

    private func handleWebSocketMessage(_ message: URLSessionWebSocketTask.Message) async {
        switch message {
        case .data(let data):
            await processMessageData(data)
        case .string(let string):
            print("📥 收到文本消息: \(string)")
        @unknown default:
            print("❓ 收到未知类型消息")
        }
    }

    private func processMessageData(_ data: Data) async {
        // 解析二进制消息 (按照API文档格式)
        guard data.count >= 8 else {
            print("❌ 消息数据太短: \(data.count) 字节")
            return
        }

        let messageType = (data[1] >> 4) & 0b1111
        print("📥 收到消息类型: \(messageType)")

        switch messageType {
        case 0b1011: // AudioOnlyServer (0x000B)
            await handleAudioData(data)
        case 0b1100: // FrontEndResultServer (0x000C)
            await handleResultData(data)
        case 0b1111: // Error (0x000F)
            await handleErrorData(data)
        default:
            print("📥 未处理的消息类型: \(messageType)")
        }
    }

    private func handleAudioData(_ data: Data) async {
        // 提取音频数据（按照API文档格式）
        let headerSize = 4 * Int(data[0] & 0b1111)
        guard data.count >= headerSize else {
            print("❌ 音频数据格式错误: headerSize=\(headerSize), dataSize=\(data.count)")
            return
        }

        let flag = data[1] & 0b1111
        var offset = headerSize

        // 如果有sequence字段，需要读取它
        var sequence: Int32? = nil
        if flag == 0b01 || flag == 0b11 { // PositiveSeq or NegativeSeq
            guard data.count >= offset + 4 else {
                print("❌ 音频数据不完整: 无法读取sequence")
                return
            }
            let sequenceData = data.subdata(in: offset..<offset+4)
            sequence = sequenceData.withUnsafeBytes { $0.load(as: Int32.self).bigEndian }
            offset += 4
            print("📊 音频序列号: \(sequence!)")
        }

        // 读取payload大小 (大端序)
        guard data.count >= offset + 4 else {
            print("❌ 音频数据不完整: 无法读取payload大小")
            return
        }

        let payloadSizeData = data.subdata(in: offset..<offset+4)
        let payloadSize = payloadSizeData.withUnsafeBytes { $0.load(as: UInt32.self).bigEndian }
        offset += 4

        guard data.count >= offset + Int(payloadSize) else {
            print("❌ 音频数据不完整: 期望\(payloadSize)字节，实际\(data.count - offset)字节")
            return
        }

        // 提取音频数据
        let audioChunk = data.subdata(in: offset..<offset+Int(payloadSize))
        audioData.append(audioChunk)
        audioChunks.append(audioChunk)

        print("🎵 收到音频数据: \(audioChunk.count) 字节，总计: \(audioData.count) 字节")

        // 流式播放：收到第一块音频数据就开始播放
        if !isStreamingStarted && audioData.count > 8192 { // 8KB缓冲后开始播放
            print("🎵 开始流式播放 (缓冲: \(audioData.count) 字节)")
            isStreamingStarted = true
            await startStreamingPlayback()
        }

        // 检查是否是最后一块数据 (sequence < 0)
        if let seq = sequence, seq < 0 {
            print("🎵 音频数据接收完成 (sequence=\(seq))，流式播放模式")
            if !isStreamingStarted {
                // 如果还没开始流式播放，直接播放所有数据
                debugAudioData()
                await playAudioData()
            }
        } else if flag == 0b11 { // NegativeSeq without sequence field
            print("🎵 音频数据接收完成 (NegativeSeq)，流式播放模式")
            if !isStreamingStarted {
                // 如果还没开始流式播放，直接播放所有数据
                debugAudioData()
                await playAudioData()
            }
        }
    }

    // MARK: - 音频数据调试
    private func debugAudioData() {
        print("🔍 音频数据调试信息:")
        print("   - 总大小: \(audioData.count) 字节")

        if audioData.count >= 44 {
            // 检查WAV文件头
            let header = audioData.prefix(44)
            let riffSignature = String(data: header.prefix(4), encoding: .ascii) ?? "未知"
            let waveSignature = String(data: header.dropFirst(8).prefix(4), encoding: .ascii) ?? "未知"

            print("   - RIFF签名: \(riffSignature)")
            print("   - WAVE签名: \(waveSignature)")

            if riffSignature == "RIFF" && waveSignature == "WAVE" {
                print("   - ✅ 检测到有效的WAV文件头")
            } else {
                print("   - ❌ 无效的WAV文件头")
            }
        } else {
            print("   - ❌ 音频数据太小，无法包含完整的WAV头")
        }
    }

    private func handleResultData(_ data: Data) async {
        print("📊 收到结果数据")
    }

    private func handleErrorData(_ data: Data) async {
        // 解析错误信息
        let headerSize = 4 * Int(data[0] & 0b1111)
        if data.count > headerSize + 8 {
            let errorCodeData = data.subdata(in: headerSize..<headerSize+4)
            let errorCode = errorCodeData.withUnsafeBytes { $0.load(as: UInt32.self).bigEndian }

            let payloadSizeData = data.subdata(in: headerSize+4..<headerSize+8)
            let payloadSize = payloadSizeData.withUnsafeBytes { $0.load(as: UInt32.self).bigEndian }

            if data.count >= headerSize + 8 + Int(payloadSize) {
                let errorPayload = data.subdata(in: headerSize+8..<headerSize+8+Int(payloadSize))
                let errorMessage = String(data: errorPayload, encoding: .utf8) ?? "未知错误"

                await MainActor.run {
                    self.errorMessage = "TTS服务错误 (\(errorCode)): \(errorMessage)"
                    self.currentState = .error("服务器错误")
                }
                print("❌ TTS服务错误 (\(errorCode)): \(errorMessage)")
                return
            }
        }

        await MainActor.run {
            errorMessage = "TTS服务返回错误"
            currentState = .error("服务器错误")
        }
        print("❌ 收到错误数据")
    }

    private func handleWebSocketError(_ error: Error) async {
        isWebSocketConnected = false

        print("❌ WebSocket错误: \(error)")

        // 检查是否是网络错误，如果是则尝试重连
        let nsError = error as NSError
        if nsError.domain == NSURLErrorDomain {
            print("🔄 检测到网络错误，尝试重连...")
            await retryConnection()
        } else {
            await MainActor.run {
                errorMessage = "WebSocket连接错误: \(error.localizedDescription)"
                currentState = .error("连接错误")
            }
        }
    }

    // MARK: - 流式音频播放
    private func startStreamingPlayback() async {
        guard !audioData.isEmpty else {
            print("❌ 没有音频数据可播放")
            return
        }

        do {
            print("🎵 开始流式播放，当前数据: \(audioData.count) 字节")

            // 重新激活音频会话
            let audioSession = AVAudioSession.sharedInstance()
            try? audioSession.setActive(false, options: .notifyOthersOnDeactivation)
            try audioSession.setCategory(.playback, mode: .default, options: [])
            try audioSession.setActive(true)

            let player = try AVAudioPlayer(data: audioData)
            player.delegate = self
            player.volume = 1.0

            await MainActor.run {
                audioPlayer = player
                isPlaying = true
                currentState = .playing
            }

            guard player.prepareToPlay() && player.play() else {
                print("❌ 流式播放启动失败")
                await MainActor.run {
                    isPlaying = false
                    currentState = .error("播放失败")
                }
                return
            }

            print("🔊 流式播放开始 (音色: \(defaultVoice))，当前时长: \(player.duration) 秒")

        } catch {
            print("❌ 流式播放失败: \(error)")
            await MainActor.run {
                errorMessage = "流式播放失败: \(error.localizedDescription)"
                currentState = .error("播放失败")
            }
        }
    }

    // MARK: - 音频播放
    private func playAudioData() async {
        guard !audioData.isEmpty else {
            print("❌ 没有音频数据可播放")
            await MainActor.run {
                currentState = .error("无音频数据")
            }
            return
        }

        do {
            print("🎵 准备播放音频数据，大小: \(audioData.count) 字节")

            // 尝试更简单的音频会话配置
            let audioSession = AVAudioSession.sharedInstance()

            // 先停用当前会话
            try? audioSession.setActive(false, options: .notifyOthersOnDeactivation)

            // 设置简单的播放类别
            try audioSession.setCategory(.playback, mode: .default, options: [])
            try audioSession.setActive(true)

            print("✅ 音频会话重新配置成功")

            let player = try AVAudioPlayer(data: audioData)
            player.delegate = self
            player.volume = 1.0  // 确保音量最大

            await MainActor.run {
                audioPlayer = player
                isPlaying = true
                currentState = .playing
            }

            // 确保音频播放器准备就绪
            let prepareResult = player.prepareToPlay()
            print("🎵 音频播放器准备结果: \(prepareResult)")

            if !prepareResult {
                print("❌ 音频播放器准备失败")
                await MainActor.run {
                    errorMessage = "音频播放器准备失败"
                    currentState = .error("播放失败")
                    isPlaying = false
                }
                return
            }

            // 开始播放
            let playResult = player.play()
            print("🎵 音频播放启动结果: \(playResult)")

            if !playResult {
                print("❌ 音频播放启动失败，可能的原因:")
                print("   - 音频格式不支持")
                print("   - 音频数据损坏")
                print("   - 音频会话配置问题")

                await MainActor.run {
                    errorMessage = "音频播放启动失败"
                    currentState = .error("播放失败")
                    isPlaying = false
                }
                return
            }

            print("🔊 开始播放TTS音频 (音色: \(defaultVoice))，时长: \(player.duration) 秒，音量: \(player.volume)")
            print("🎵 音频格式: 采样率=\(player.format.sampleRate), 通道数=\(player.format.channelCount)")
            print("🎵 播放状态: isPlaying=\(player.isPlaying)")

        } catch {
            await MainActor.run {
                errorMessage = "音频播放失败: \(error.localizedDescription)"
                currentState = .error("播放失败")
                isPlaying = false
            }
            print("❌ 音频播放失败: \(error)")
            print("❌ 错误详情: \(error)")
        }
    }

    // MARK: - 心跳机制
    private func startHeartbeat() {
        heartbeatTimer?.invalidate()
        heartbeatTimer = Timer.scheduledTimer(withTimeInterval: 30.0, repeats: true) { [weak self] _ in
            self?.sendHeartbeat()
        }
    }

    private func sendHeartbeat() {
        guard let webSocketTask = webSocketTask, webSocketTask.state == .running else {
            print("💔 心跳检测：连接已断开")
            isWebSocketConnected = false
            return
        }

        // 发送ping消息
        webSocketTask.sendPing { [weak self] error in
            if let error = error {
                print("💔 心跳失败: \(error)")
                self?.isWebSocketConnected = false
            } else {
                print("💓 心跳正常")
            }
        }
    }

    // MARK: - 连接重试
    private func retryConnection() async {
        guard connectionRetryCount < maxRetryCount else {
            print("❌ 连接重试次数已达上限")
            await MainActor.run {
                errorMessage = "连接失败，请稍后重试"
                currentState = .error("连接失败")
            }
            return
        }

        connectionRetryCount += 1
        print("🔄 尝试重新连接 (\(connectionRetryCount)/\(maxRetryCount))")

        // 等待一段时间后重试
        try? await Task.sleep(nanoseconds: 2_000_000_000) // 2秒

        if !currentText.isEmpty {
            await connectAndSynthesize(text: currentText)
        }
    }

    deinit {
        // 清理资源 - 不能在deinit中调用主线程方法
        audioPlayer?.stop()
        audioPlayer = nil
        heartbeatTimer?.invalidate()
        heartbeatTimer = nil
        webSocketTask?.cancel(with: .goingAway, reason: nil)
        webSocketTask = nil
        urlSession?.invalidateAndCancel()
    }
}

// MARK: - URLSessionWebSocketDelegate
extension TTSService: URLSessionWebSocketDelegate {
    nonisolated func urlSession(_ session: URLSession, webSocketTask: URLSessionWebSocketTask, didOpenWithProtocol protocol: String?) {
        print("✅ WebSocket连接已建立")
        Task { @MainActor in
            isWebSocketConnected = true
            connectionRetryCount = 0
        }
    }

    nonisolated func urlSession(_ session: URLSession, webSocketTask: URLSessionWebSocketTask, didCloseWith closeCode: URLSessionWebSocketTask.CloseCode, reason: Data?) {
        print("🔌 WebSocket连接已关闭: \(closeCode)")
        Task { @MainActor in
            isWebSocketConnected = false
            heartbeatTimer?.invalidate()
            heartbeatTimer = nil

            // 如果不是正常关闭且有待处理的文本，尝试重连
            if closeCode != .normalClosure && !currentText.isEmpty {
                Task {
                    await retryConnection()
                }
            }
        }
    }
}

// MARK: - AVAudioPlayerDelegate
extension TTSService: AVAudioPlayerDelegate {
    nonisolated func audioPlayerDidFinishPlaying(_ player: AVAudioPlayer, successfully flag: Bool) {
        Task { @MainActor in
            print("✅ TTS音频播放完成 (音色: \(defaultVoice))")

            // 重置播放状态
            isPlaying = false
            playbackProgress = 1.0
            currentState = .idle
            currentText = ""

            // 清理音频数据和流式播放状态
            audioData = Data()
            audioChunks.removeAll()
            isStreamingStarted = false

            // 清理音频播放器
            audioPlayer = nil

            print("🔄 TTS播放状态已重置，准备接受新的语音输入")
        }
    }

    nonisolated func audioPlayerDecodeErrorDidOccur(_ player: AVAudioPlayer, error: Error?) {
        Task { @MainActor in
            errorMessage = "音频解码错误: \(error?.localizedDescription ?? "未知错误")"
            currentState = .error("解码错误")
            isPlaying = false
        }
    }
}