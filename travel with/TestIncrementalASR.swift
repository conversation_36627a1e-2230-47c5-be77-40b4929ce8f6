//
//  TestIncrementalASR.swift
//  travel with
//
//  Created by AI Assistant on 2025/8/1.
//

import Foundation

// MARK: - 增量ASR结果测试
class TestIncrementalASR {
    
    static func testIncrementalResults() {
        print("🚀 测试增量ASR结果处理")
        print("=" * 50)
        
        // 模拟增量结果序列
        let incrementalResults = [
            "我",
            "现在",
            "，呃，",
            "你想",
            "唱歌",
            "吗？"
        ]
        
        var accumulatedText = ""
        
        print("📝 模拟增量结果处理过程:")
        for (index, increment) in incrementalResults.enumerated() {
            print("🎤 增量识别 \(index + 1): \(increment)")
            
            // 累积增量结果
            if accumulatedText.isEmpty {
                accumulatedText = increment
            } else {
                accumulatedText += increment
            }
            
            print("📝 累积文本: \(accumulatedText)")
            print("---")
        }
        
        print("✅ 最终累积结果: \(accumulatedText)")
        print("🤖 将发送给AI修正: \(accumulatedText)")
        
        print("=" * 50)
        print("🎯 增量结果测试完成")
        
        // 预期的AI修正结果
        print("💡 预期AI修正后: 我现在，呃，你想唱歌吗？")
    }
}

// MARK: - 字符串扩展
extension String {
    static func * (left: String, right: Int) -> String {
        return String(repeating: left, count: right)
    }
}
